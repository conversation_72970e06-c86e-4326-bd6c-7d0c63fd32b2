import { eq } from "drizzle-orm";
import { database } from "~/database/context";
import { seminarians } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params }: Route.LoaderArgs) {
  if(params.id == 'new') return {
    fullName: 'New Seminarian'
  };

  const db = database();
  const seminarianId = parseInt(params.id, 10);

  if (isNaN(seminarianId)) {
    throw new Response("Invalid seminarian ID", { status: 400 });
  }

  // Get seminarian basic info only
  const seminarianData = await db.query.seminarians.findFirst({
    where: eq(seminarians.id, seminarianId),
    columns: {
      firstName: true,
      lastName: true,
      middleName: true,
    }
  });

  if (!seminarianData) {
    throw new Response("Seminarian not found", { status: 404 });
  }

  const fullName = [seminarianData.firstName, seminarianData.middleName, seminarianData.lastName].filter(Boolean).join(" ");

  return {
    fullName,
  };
}
